<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Surveillance Equipment Payback Period Calculator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .problem-statement {
            background: #e8f4f8;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            border-left: 4px solid #3498db;
        }
        
        .input-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .input-group {
            display: flex;
            flex-direction: column;
        }
        
        label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #34495e;
        }
        
        input {
            padding: 10px;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            font-size: 16px;
        }
        
        input:focus {
            border-color: #3498db;
            outline: none;
        }
        
        .calculate-btn {
            background: #3498db;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            cursor: pointer;
            margin: 20px auto;
            display: block;
        }
        
        .calculate-btn:hover {
            background: #2980b9;
        }
        
        .results {
            margin-top: 30px;
        }
        
        .payback-result {
            background: #d5f4e6;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        th, td {
            padding: 12px;
            text-align: right;
            border: 1px solid #ddd;
        }
        
        th {
            background: #34495e;
            color: white;
        }
        
        .payback-year {
            background: #f1c40f !important;
            font-weight: bold;
        }
        
        .negative {
            color: #e74c3c;
        }
        
        .positive {
            color: #27ae60;
        }
        
        .formula {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
            border-left: 4px solid #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Problem 08.033 - Payback Period Calculator</h1>
        
        <div class="problem-statement">
            <h3>Problem Statement:</h3>
            <p>The Sundance Detective Agency has purchased new surveillance equipment. Calculate the payback period to make a return of 10% per year.</p>
        </div>
        
        <div class="input-section">
            <div class="input-group">
                <label for="firstCost">First Cost ($)</label>
                <input type="number" id="firstCost" value="1200" step="0.01">
            </div>
            
            <div class="input-group">
                <label for="maintenanceBase">Annual Maintenance Base ($)</label>
                <input type="number" id="maintenanceBase" value="75" step="0.01">
            </div>
            
            <div class="input-group">
                <label for="maintenanceIncrement">Maintenance Increment ($/year)</label>
                <input type="number" id="maintenanceIncrement" value="5" step="0.01">
            </div>
            
            <div class="input-group">
                <label for="revenueBase">Annual Revenue Base ($)</label>
                <input type="number" id="revenueBase" value="250" step="0.01">
            </div>
            
            <div class="input-group">
                <label for="revenueIncrement">Revenue Increment ($/year)</label>
                <input type="number" id="revenueIncrement" value="50" step="0.01">
            </div>
            
            <div class="input-group">
                <label for="salvageValue">Salvage Value ($)</label>
                <input type="number" id="salvageValue" value="750" step="0.01">
            </div>
            
            <div class="input-group">
                <label for="discountRate">Required Return Rate (%)</label>
                <input type="number" id="discountRate" value="10" step="0.1">
            </div>
        </div>
        
        <button class="calculate-btn" onclick="calculatePayback()">Calculate Payback Period</button>
        
        <div class="formula">
            <strong>Formulas Used:</strong><br>
            Annual Maintenance Cost (year k) = Base + Increment × k<br>
            Annual Revenue (year k) = Base + Increment × k<br>
            Net Cash Flow (year k) = Revenue - Maintenance<br>
            Present Value = Cash Flow / (1 + discount rate)^k<br>
            NPV = -Initial Cost + Σ(PV of Cash Flows) + PV of Salvage Value
        </div>
        
        <div class="results" id="results" style="display: none;">
            <div class="payback-result" id="paybackResult"></div>
            <table id="cashFlowTable">
                <thead>
                    <tr>
                        <th>Year</th>
                        <th>Maintenance Cost</th>
                        <th>Revenue</th>
                        <th>Net Cash Flow</th>
                        <th>PV of Cash Flow</th>
                        <th>PV of Salvage</th>
                        <th>Cumulative NPV</th>
                    </tr>
                </thead>
                <tbody id="tableBody">
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function calculatePayback() {
            // Get input values
            const firstCost = parseFloat(document.getElementById('firstCost').value);
            const maintenanceBase = parseFloat(document.getElementById('maintenanceBase').value);
            const maintenanceIncrement = parseFloat(document.getElementById('maintenanceIncrement').value);
            const revenueBase = parseFloat(document.getElementById('revenueBase').value);
            const revenueIncrement = parseFloat(document.getElementById('revenueIncrement').value);
            const salvageValue = parseFloat(document.getElementById('salvageValue').value);
            const discountRate = parseFloat(document.getElementById('discountRate').value) / 100;
            
            let cumulativeNPV = -firstCost;
            let paybackYear = null;
            let paybackYearExact = null;
            const maxYears = 20;
            
            const tableBody = document.getElementById('tableBody');
            tableBody.innerHTML = '';
            
            // Year 0 row
            const row0 = tableBody.insertRow();
            row0.innerHTML = `
                <td>0</td>
                <td>-</td>
                <td>-</td>
                <td>-${firstCost.toFixed(2)}</td>
                <td>-${firstCost.toFixed(2)}</td>
                <td>-</td>
                <td class="negative">-${firstCost.toFixed(2)}</td>
            `;
            
            let previousNPV = cumulativeNPV;
            
            for (let year = 1; year <= maxYears; year++) {
                // Calculate cash flows for this year
                const maintenanceCost = maintenanceBase + maintenanceIncrement * year;
                const revenue = revenueBase + revenueIncrement * year;
                const netCashFlow = revenue - maintenanceCost;
                
                // Calculate present values
                const pvCashFlow = netCashFlow / Math.pow(1 + discountRate, year);
                const pvSalvage = salvageValue / Math.pow(1 + discountRate, year);
                
                // Update cumulative NPV
                cumulativeNPV += pvCashFlow + pvSalvage;
                
                // Check for payback
                if (paybackYear === null && cumulativeNPV >= 0) {
                    paybackYear = year;
                    // Calculate exact payback period using interpolation
                    if (previousNPV < 0) {
                        const fraction = -previousNPV / (cumulativeNPV - previousNPV);
                        paybackYearExact = (year - 1) + fraction;
                    } else {
                        paybackYearExact = year;
                    }
                }
                
                // Add row to table
                const row = tableBody.insertRow();
                const isPaybackYear = year === paybackYear;
                row.className = isPaybackYear ? 'payback-year' : '';
                
                row.innerHTML = `
                    <td>${year}</td>
                    <td>$${maintenanceCost.toFixed(2)}</td>
                    <td>$${revenue.toFixed(2)}</td>
                    <td>$${netCashFlow.toFixed(2)}</td>
                    <td>$${pvCashFlow.toFixed(2)}</td>
                    <td>$${pvSalvage.toFixed(2)}</td>
                    <td class="${cumulativeNPV >= 0 ? 'positive' : 'negative'}">$${cumulativeNPV.toFixed(2)}</td>
                `;
                
                previousNPV = cumulativeNPV;
                
                if (paybackYear && year > paybackYear + 2) break;
            }
            
            // Display results
            const resultsDiv = document.getElementById('results');
            const paybackResultDiv = document.getElementById('paybackResult');
            
            if (paybackYear) {
                paybackResultDiv.innerHTML = `
                    Payback Period: ${paybackYearExact.toFixed(2)} years
                    <br><small>(Equipment pays back with 10% return in ${paybackYearExact.toFixed(2)} years)</small>
                `;
            } else {
                paybackResultDiv.innerHTML = `
                    No payback within ${maxYears} years at 10% return rate
                `;
                paybackResultDiv.style.background = '#f8d7da';
                paybackResultDiv.style.color = '#721c24';
            }
            
            resultsDiv.style.display = 'block';
        }
        
        // Calculate on page load
        window.onload = function() {
            calculatePayback();
        };
    </script>
</body>
</html>
